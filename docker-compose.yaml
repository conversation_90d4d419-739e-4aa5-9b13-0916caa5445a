version: '3.8'

services:
  index-tts:
    build: .
    image: index-tts-server
    container_name: index-tts-server
    ports:
      - "8001:8001"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    # volumes:
    #   - ./audio_prompts:/app/audio_prompts
    #   - ./checkpoints:/app/checkpoints
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3